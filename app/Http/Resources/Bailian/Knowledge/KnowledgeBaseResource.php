<?php

namespace App\Http\Resources\Bailian\Knowledge;

use App\Models\SystemConfig;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Modules\User\Http\Resources\UserBaseInfoResource;

class KnowledgeBaseResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        $user = $request->kernel->user();
        return [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'bailian_knowledge_id' => $this->knowledge_id,
            'description'          => $this->description,
            'user'                 => $this->getUserResource($this->user),
            'status'               => $this->status,
            'can'                  => $this->getCan($user),
            'count'                => [
                'items_count'   => $this->items_count ?? 0,
                'members_count' => max($this->members_count, 1),
            ],
            'level'                => [
                'value' => $this->level->value,
                'text'  => $this->level->toString(),
            ],
            'auto_join'            => (bool) $this->auto_join,
            'created_at'           => (string) $this->created_at,
            'diff_for_humans'      => $this->created_at->diffForHumans(),
        ];
    }

    public function getUserResource($user)
    {
        if ($user && $user->id > 0) {
            return new UserBaseInfoResource($user);
        }
        $sysDefAvatar = SystemConfig::getValue('sys_def_avatar', '/avatar/wate-nan.jpg');
        return [
            'user_id'  => '',
            'username' => '',
            'nickname' => '瓦特',
            'avatar'   => Storage::url($sysDefAvatar),
        ];
    }
}
