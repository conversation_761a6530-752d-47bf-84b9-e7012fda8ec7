<?php

namespace App\Http\Resources\Bailian\Knowledge;

use App\Http\Resources\Bailian\CategoryResource;
use App\Http\Resources\InteractionResourceTrait;
use App\Models\SystemConfig;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Modules\User\Http\Resources\UserBaseInfoResource;

class KnowledgeResource extends JsonResource
{
    use InteractionResourceTrait;

    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        $user = $request->kernel->user();
        return [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'bailian_knowledge_id' => $this->knowledge_id,
            'user'                 => $this->getUserResource($this->user),
            'description'          => $this->description,
            'source_type'          => [
                'value' => $this->source_type,
                'text'  => $this->source_type_text
            ],
            'sink_type'            => [
                'value' => $this->sink_type,
                'text'  => $this->sink_type_text,
            ],
            'status'               => $this->status,
            'level'                => [
                'value' => $this->level->value,
                'text'  => $this->level->toString(),
            ],
            'can'                  => $this->getCan($user),
            'count'                => $this->getCount(),
            'base_size'            => $this->getBaseSize(),
            'is_featured'          => $this->is_featured,
            'auto_join'            => (bool) $this->auto_join,
            'join_status'          => $this->getJoinStatus($user),
            'categories'           => $this->categories ? CategoryResource::collection($this->categories) : null,
            'interaction'          => $this->getInteraction(),
            'share_url'            => $this->getShareUrl(),
            'created_at'           => (string) $this->created_at,
        ];
    }

    public function getUserResource($user)
    {
        if ($user && $user->id > 0) {
            return new UserBaseInfoResource($user);
        }
        $sysDefAvatar = SystemConfig::getValue('sys_def_avatar', '/avatar/wate-nan.jpg');
        return [
            'user_id'  => '',
            'username' => '',
            'nickname' => '瓦特',
            'avatar'   => Storage::url($sysDefAvatar),
        ];
    }
}
