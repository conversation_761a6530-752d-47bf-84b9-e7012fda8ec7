<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Http\Controllers\ApiController;
use App\Http\Requests\Bailian\AudioToTextRequest;
use App\Http\Requests\Bailian\ImageToTextRequest;
use App\Http\Requests\Bailian\UrlToTextRequest;
use App\Http\Resources\Bailian\Article\ArticleBaseResource;
use App\Http\Resources\Bailian\Knowledge\KnowledgeBaseResource;
use App\Models\BailianArticle;
use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Http\Request;

class IndexController extends ApiController
{
    public function index(Request $request)
    {
        $user         = $request->kernel->user();
        $article_type = $request->article_type;
        $orderBy      = $request->article_order_by ?? 'created_at';
        $sortType     = $request->article_sort_type ?? 'desc';

        $knowledge = BailianKnowledge::query()
            ->OfCount()
            ->ofType('my', $user)
            ->with(['categories'])
            ->latest()
            ->take(10)
            ->get();
        $articles  = BailianArticle::query()
            ->ofUser($user)
            ->when($article_type, function ($query) use ($article_type) {
                return $query->where('type', $article_type);
            })
            ->orderBy($orderBy, $sortType)
            ->take(10)
            ->get();
        $data      = [
            'knowledge' => KnowledgeBaseResource::collection($knowledge),
            'articles'  => ArticleBaseResource::collection($articles),
        ];
        \Log::info('测试');
        return $request->kernel->success($data);
    }

    /**
     * Notes: 音频转文字
     *
     * @Author: 玄尘
     * @Date: 2025/5/22 10:59
     * @param  \App\Http\Requests\Bailian\AudioToTextRequest  $request
     * @return mixed
     */
    public function audioToText(AudioToTextRequest $request)
    {
        $path_url = $request->path_url;
        if (! $path_url) {
            return $request->kernel->error('缺少音频地址');
        }
        $res = app(BLAssistant::class)->understanding()->audio($path_url);
        if (! $res['status']) {
            return $request->kernel->error($res['message']);
        }
        $output = json_decode($res['data']['output'], true);
        return $request->kernel->success($output);
    }

    public function imageToText(ImageToTextRequest $request)
    {
        config([
            'app.debug' => false,
        ]);
        if (function_exists('apache_setenv')) {
            apache_setenv('no-gzip', 1);
        }
        ini_set('output_buffering', 'off');
        ini_set('zlib.output_compression', false);
        ini_set('implicit_flush', true);
        while (ob_get_level() > 0) {
            ob_end_flush();
        }
        ob_start();
        ignore_user_abort(true);
        set_time_limit(0);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush();
        $path_url = $request->path_url;
        $prompt   = $request->prompt ?? '';
        if (! $path_url) {
            return $request->kernel->error('缺少图片地址');
        }
        $res = app(BLAssistant::class)->understanding()->image($path_url, $prompt);
        if (! $res['status']) {
            $request->kernel->sseError($res['message']);
        }
        $outPut = $res['data']['output'];
        $outPut = str_replace("```json", "", $outPut);
        $outPut = str_replace("```", "", $outPut);
        $outPut = json_decode($outPut, true);
        info($outPut);
        $title = $outPut['title'] ?? '';
        if (! blank($title)) {
            $request->kernel->sseEcho($title, 'title');
        }
        foreach (mb_str_split($outPut['article'] ?? '', 1) ?: [] as $char) {
            $request->kernel->sseEcho($char);
        }
        $tags = $outPut['tags'] ?? [];
        foreach ($tags as $tag) {
            $request->kernel->sseEcho($tag, 'tag');
        }
        ob_end_clean();
    }

    /**
     * Notes: url转文字
     *
     * @Author: 玄尘
     * @Date: 2025/5/22 14:33
     * @param  \App\Http\Requests\Bailian\UrlToTextRequest  $request
     * @return mixed
     */
    public function urlToText(UrlToTextRequest $request)
    {
        try {
            config([
                'app.debug' => false,
            ]);
            if (function_exists('apache_setenv')) {
                apache_setenv('no-gzip', 1);
            }
            ini_set('output_buffering', 'off');
            ini_set('zlib.output_compression', false);
            ini_set('implicit_flush', true);
            while (ob_get_level() > 0) {
                ob_end_flush();
            }
            ob_start();
            ignore_user_abort(true);
            set_time_limit(0);
            session_write_close();
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
            header('Connection: keep-alive');
            header('X-Accel-Buffering: no');
            ob_implicit_flush();

            $url    = $request->url;
            $prompt = $request->prompt ?? '';
            if (! $url) {
                return $request->kernel->error('请输入url');
            }
            $data = app(BLAssistant::class)->understanding()->url($url);
            if ($data['code'] === 0) {
                $output = $data['data'];
                $title  = $output['title'] ?? '';
                if (! blank($title)) {
                    $request->kernel->sseEcho($title, 'title');
                }
                $analysis = $output['analysis'] ?? '';
                foreach (mb_str_split($analysis, 1) ?: [] as $char) {
                    $request->kernel->sseEcho($char);
                }
                $tags = $output['tags'] ?? [];
                foreach ($tags as $tag) {
                    $request->kernel->sseEcho($tag, 'tag');
                }
            } else {
                $request->kernel->sseError($data['msg']);
            }
        } catch (Exception $exception) {
            $request->kernel->sseError($exception->getMessage());
        }
        ob_end_clean();
    }

    public function polishText(Request $request)
    {
        $text = $request->text;
        if (! $text) {
            return $request->kernel->error('请输入文本');
        }
        $res = app(BLAssistant::class)->understanding()->polishText($text);
        if (! $res['status']) {
            return $request->kernel->error($res['message']);
        }

        $output = json_decode($res['data']['output'], true);

        return $request->kernel->success($output);
    }

    public function correctText(Request $request)
    {
        $text = $request->text;
        if (! $text) {
            return $request->kernel->error('请输入文本');
        }
        $res = app(BLAssistant::class)->understanding()->correctText($text);
        if (! $res['status']) {
            return $request->kernel->error($res['message']);
        }
        if ($res['data']['output'] == '请输入要纠错的文本') {
            return $request->kernel->error('请输入要纠错的文本');
        }
        $output = json_decode($res['data']['output'], true);

        return $request->kernel->success($output);
    }

}
